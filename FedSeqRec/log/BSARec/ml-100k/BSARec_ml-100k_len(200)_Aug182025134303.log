{'early_stop_enabled': True, 'model': 'BSARec', 'lr': 0.001, 'batch_size': 128, 'neg_num': 99, 'l2_reg': 0, 'l2_emb': 0.0, 'hidden_size': 50, 'dropout': 0.2, 'epochs': 1000, 'early_stop': 30, 'datapath': '../../data/', 'dataset': 'ml-100k', 'train_data': 'ml-100k.txt', 'log_path': '../log', 'num_layers': 2, 'num_heads': 1, 'inner_size': 256, 'max_seq_len': 200, 'upload_mode': 'full', 'skip_test_eval': False, 'eval_freq': 1, 'eval_mode': 'full', 'download_speed': 5.0, 'upload_speed': 5.0, 'c': 9, 'alpha': 0.3}
[Aug-18-2025_13-43-03] - 开始训练，配置参数如下：
[Aug-18-2025_13-43-03] - early_stop_enabled: True
[Aug-18-2025_13-43-03] - model: BSARec
[Aug-18-2025_13-43-03] - lr: 0.001
[Aug-18-2025_13-43-03] - batch_size: 128
[Aug-18-2025_13-43-03] - neg_num: 99
[Aug-18-2025_13-43-03] - l2_reg: 0
[Aug-18-2025_13-43-03] - l2_emb: 0.0
[Aug-18-2025_13-43-03] - hidden_size: 50
[Aug-18-2025_13-43-03] - dropout: 0.2
[Aug-18-2025_13-43-03] - epochs: 1000
[Aug-18-2025_13-43-03] - early_stop: 30
[Aug-18-2025_13-43-03] - datapath: ../../data/
[Aug-18-2025_13-43-03] - dataset: ml-100k
[Aug-18-2025_13-43-03] - train_data: ml-100k.txt
[Aug-18-2025_13-43-03] - log_path: ../log
[Aug-18-2025_13-43-03] - num_layers: 2
[Aug-18-2025_13-43-03] - num_heads: 1
[Aug-18-2025_13-43-03] - inner_size: 256
[Aug-18-2025_13-43-03] - max_seq_len: 200
[Aug-18-2025_13-43-03] - upload_mode: full
[Aug-18-2025_13-43-03] - skip_test_eval: False
[Aug-18-2025_13-43-03] - eval_freq: 1
[Aug-18-2025_13-43-03] - eval_mode: full
[Aug-18-2025_13-43-03] - download_speed: 5.0
[Aug-18-2025_13-43-03] - upload_speed: 5.0
[Aug-18-2025_13-43-03] - c: 9
[Aug-18-2025_13-43-03] - alpha: 0.3
[Aug-18-2025_13-43-03] - 训练数据: ../../data/ml-100k/ml-100k.txt
[Aug-18-2025_13-43-03] - 最大序列长度: 200
[Aug-18-2025_13-43-03] - 批次大小: 128
[Aug-18-2025_13-43-04] - 参数上传模式: full
[Aug-18-2025_13-43-04] - 隐私参数（本地更新）: []
[Aug-18-2025_13-43-04] - 非隐私参数（服务器聚合）: ['item_embedding.weight', 'position_embedding.weight', 'encoder.blocks.0.layer.filter_layer.sqrt_beta', 'encoder.blocks.0.layer.filter_layer.LayerNorm.weight', 'encoder.blocks.0.layer.filter_layer.LayerNorm.bias', 'encoder.blocks.0.layer.attention_layer.query.weight', 'encoder.blocks.0.layer.attention_layer.query.bias', 'encoder.blocks.0.layer.attention_layer.key.weight', 'encoder.blocks.0.layer.attention_layer.key.bias', 'encoder.blocks.0.layer.attention_layer.value.weight', 'encoder.blocks.0.layer.attention_layer.value.bias', 'encoder.blocks.0.layer.attention_layer.dense.weight', 'encoder.blocks.0.layer.attention_layer.dense.bias', 'encoder.blocks.0.layer.attention_layer.LayerNorm.weight', 'encoder.blocks.0.layer.attention_layer.LayerNorm.bias', 'encoder.blocks.0.feed_forward.dense_1.weight', 'encoder.blocks.0.feed_forward.dense_1.bias', 'encoder.blocks.0.feed_forward.dense_2.weight', 'encoder.blocks.0.feed_forward.dense_2.bias', 'encoder.blocks.0.feed_forward.LayerNorm.weight', 'encoder.blocks.0.feed_forward.LayerNorm.bias', 'encoder.blocks.1.layer.filter_layer.sqrt_beta', 'encoder.blocks.1.layer.filter_layer.LayerNorm.weight', 'encoder.blocks.1.layer.filter_layer.LayerNorm.bias', 'encoder.blocks.1.layer.attention_layer.query.weight', 'encoder.blocks.1.layer.attention_layer.query.bias', 'encoder.blocks.1.layer.attention_layer.key.weight', 'encoder.blocks.1.layer.attention_layer.key.bias', 'encoder.blocks.1.layer.attention_layer.value.weight', 'encoder.blocks.1.layer.attention_layer.value.bias', 'encoder.blocks.1.layer.attention_layer.dense.weight', 'encoder.blocks.1.layer.attention_layer.dense.bias', 'encoder.blocks.1.layer.attention_layer.LayerNorm.weight', 'encoder.blocks.1.layer.attention_layer.LayerNorm.bias', 'encoder.blocks.1.feed_forward.dense_1.weight', 'encoder.blocks.1.feed_forward.dense_1.bias', 'encoder.blocks.1.feed_forward.dense_2.weight', 'encoder.blocks.1.feed_forward.dense_2.bias', 'encoder.blocks.1.feed_forward.LayerNorm.weight', 'encoder.blocks.1.feed_forward.LayerNorm.bias', 'LayerNorm.weight', 'LayerNorm.bias']
[Aug-18-2025_13-43-04] - 用户数量: 943
[Aug-18-2025_13-43-04] - 物品数量: 1349
[Aug-18-2025_13-43-07] - --- Latency & Communication Simulation (Epoch 0, Batch 0) ---
[Aug-18-2025_13-43-07] - 客户端下载模型大小: 588.13 KB
[Aug-18-2025_13-43-07] - 客户端平均上传梯度大小: 588.13 KB
[Aug-18-2025_13-43-07] - 网络速度 (下载/上传): 5.0/5.0 MB/s
[Aug-18-2025_13-43-07] - 平均下载延迟: 0.1149 s
[Aug-18-2025_13-43-07] - 平均上传延迟: 0.1149 s
[Aug-18-2025_13-43-07] - 单轮平均总通信延迟: 0.2297 s
[Aug-18-2025_13-43-07] - ----------------------------------------------------------
[Aug-18-2025_13-43-24] - --- Epoch 0 Communication Summary ---
[Aug-18-2025_13-43-24] - 943个客户端下载数据合计: 541.61 MB
[Aug-18-2025_13-43-24] - 943个客户端上传数据合计: 541.61 MB
[Aug-18-2025_13-43-24] - 943个客户端总通信延迟合计: 216.64 s
[Aug-18-2025_13-43-24] - ------------------------------------
[Aug-18-2025_13-43-35] - epoch:1, time: 30.799826(s), valid (NDCG@10: 0.0039, HR@10: 0.0095), test (NDCG@10: 0.0012, HR@10: 0.0032), all_time: 30.799826(s)
[Aug-18-2025_13-43-35] - 新的最佳性能: valid NDCG@10=0.0039, test NDCG@10=0.0012
[Aug-18-2025_13-44-06] - epoch:2, time: 31.315030(s), valid (NDCG@10: 0.0007, HR@10: 0.0021), test (NDCG@10: 0.0008, HR@10: 0.0021), all_time: 62.114856(s)
[Aug-18-2025_13-44-36] - epoch:3, time: 29.302042(s), valid (NDCG@10: 0.0007, HR@10: 0.0021), test (NDCG@10: 0.0014, HR@10: 0.0042), all_time: 91.416898(s)
[Aug-18-2025_13-44-36] - 新的最佳性能: valid NDCG@10=0.0039, test NDCG@10=0.0014
